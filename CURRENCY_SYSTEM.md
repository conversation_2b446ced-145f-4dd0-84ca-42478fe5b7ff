## Currency Detection & Management System

### Overview
Implemented a comprehensive currency detection and management system for the subscription service that automatically detects user location and provides manual currency selection options.

### Features Implemented

#### 🌍 **Automatic Detection**
- **Location-based**: Uses GPS/location services to detect if user is in India
- **System locale**: Falls back to device language/region settings  
- **Header-based**: Server detects country from request headers (Cloudflare, etc.)
- **Default fallback**: USD for international users, INR for Indian users

#### 💰 **Currency Support**
- **India**: ₹ (INR) - Full local pricing in Indian Rupees
- **International**: $ (USD) - Converted pricing for global users
- **Conversion ratio**: 1 USD ≈ 83 INR (configurable)

#### 🔧 **Manual Selection**
- Currency selector button in subscription screen header
- User can override auto-detection
- Preferences saved both locally and on server
- Persists across app sessions and devices

#### 🔄 **Backend Integration**
- User preferences API endpoints (`/auth/preferences`)
- Currency preference stored in User model
- Server-side currency detection from headers
- Dynamic pricing based on detected/saved currency

### Technical Implementation

#### **Frontend (React Native)**
- `CurrencyService` - Handles detection, storage, and conversion
- `expo-location` - For GPS-based country detection  
- AsyncStorage - Local currency preference caching
- Alert dialogs - Manual currency selection UI

#### **Backend (Node.js)**
- Updated `User` model with currency preference field
- New API endpoints for preferences management
- Enhanced `currencyUtils` for simplified detection
- Multi-currency pricing in subscription plans

#### **Currency Flow**
1. **Auto-detect**: Location → Locale → Headers → Default (USD)
2. **Manual override**: User selects preferred currency
3. **Save preferences**: Local storage + server database
4. **Price conversion**: Dynamic pricing in user's currency
5. **Payment flow**: Currency included in payment processing

### Usage Example

```javascript
// Auto-detect user currency
const currency = await CurrencyService.getUserCurrency();

// Manual selection
const newCurrency = await CurrencyService.showCurrencySelector();

// Price formatting
const formattedPrice = CurrencyService.formatPrice(149, currency, 'month', 1);
// Result: "₹149/month" or "$1.99/month"
```

### Benefits

✅ **User Experience**: Automatic currency detection without user intervention  
✅ **Global Support**: Works for both Indian and international users  
✅ **Flexibility**: Users can manually override auto-detection  
✅ **Persistence**: Preferences saved across sessions and devices  
✅ **Fallback Logic**: Multiple detection methods ensure reliability  
✅ **Backend Integration**: Server-aware currency preferences for consistency

The system now automatically shows Indian users pricing in ₹ (INR) and international users pricing in $ (USD), with options to manually change currency preferences that persist across the app and sync with the backend.
