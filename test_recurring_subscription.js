/**
 * Test script for recurring subscription functionality
 * Tests both INR and USD currency flows
 */

import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3000/api/v1';
const TEST_TOKEN = 'your-test-jwt-token'; // Replace with actual test token

// Test data
const TEST_PLANS = ['basic_monthly', 'standard_quarterly', 'pro_yearly'];
const TEST_CURRENCIES = [
  { code: 'INR', symbol: '₹', name: 'Indian Rupee' },
  { code: 'USD', symbol: '$', name: 'US Dollar' }
];

/**
 * Make API request with authentication
 */
async function makeRequest(endpoint, method = 'GET', body = null, headers = {}) {
  const url = `${BASE_URL}${endpoint}`;
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${TEST_TOKEN}`,
      ...headers
    }
  };

  if (body) {
    options.body = JSON.stringify(body);
  }

  console.log(`🌐 ${method} ${url}`);
  if (body) console.log('📤 Request body:', body);

  try {
    const response = await fetch(url, options);
    const data = await response.json();
    
    console.log(`📥 Response (${response.status}):`, data);
    return { success: response.ok, status: response.status, data };
  } catch (error) {
    console.error('❌ Request failed:', error.message);
    return { success: false, error: error.message };
  }
}

/**
 * Test recurring subscription creation for a specific currency
 */
async function testRecurringSubscription(planId, currency) {
  console.log(`\n🧪 Testing recurring subscription: ${planId} in ${currency.code}`);
  console.log('='.repeat(60));

  // Simulate request headers for currency detection
  const headers = currency.code === 'INR' ? 
    { 'Accept-Language': 'en-IN', 'CF-IPCountry': 'IN' } :
    { 'Accept-Language': 'en-US', 'CF-IPCountry': 'US' };

  const result = await makeRequest('/subscriptions/create-recurring', 'POST', {
    planId,
    currency: currency.code,
    customerInfo: {
      name: 'Test User',
      phone: '+1234567890'
    }
  }, headers);

  if (result.success) {
    console.log('✅ Recurring subscription created successfully');
    console.log('📋 Subscription details:');
    console.log(`   - ID: ${result.data.data?.subscriptionId}`);
    console.log(`   - Razorpay ID: ${result.data.data?.razorpaySubscriptionId}`);
    console.log(`   - Plan: ${result.data.data?.planDetails?.name}`);
    console.log(`   - Price: ${result.data.data?.planDetails?.currencySymbol}${result.data.data?.planDetails?.price}`);
    console.log(`   - Currency: ${result.data.data?.planDetails?.currency}`);
    console.log(`   - Next billing: ${result.data.data?.nextBillingDate}`);
    console.log(`   - Auto-renewal: ${result.data.data?.autoRenewal}`);
  } else {
    console.log('❌ Recurring subscription creation failed');
    console.log(`   - Status: ${result.status}`);
    console.log(`   - Error: ${result.data?.message || result.error}`);
  }

  return result;
}

/**
 * Test plan details retrieval
 */
async function testPlanDetails() {
  console.log('\n🧪 Testing plan details retrieval');
  console.log('='.repeat(60));

  const result = await makeRequest('/subscriptions/plans');
  
  if (result.success) {
    console.log('✅ Plans retrieved successfully');
    console.log('📋 Available plans:');
    result.data.data?.plans?.forEach(plan => {
      console.log(`   - ${plan.id}: ${plan.name} - ${plan.currencySymbol}${plan.price}/${plan.interval}`);
    });
  } else {
    console.log('❌ Failed to retrieve plans');
  }

  return result;
}

/**
 * Test currency detection
 */
async function testCurrencyDetection() {
  console.log('\n🧪 Testing currency detection');
  console.log('='.repeat(60));

  // Test Indian headers
  console.log('🇮🇳 Testing Indian user detection:');
  let result = await makeRequest('/subscriptions/plans', 'GET', null, {
    'Accept-Language': 'en-IN',
    'CF-IPCountry': 'IN',
    'User-Agent': 'Mozilla/5.0 (Linux; Android 10; India) AppleWebKit/537.36'
  });

  // Test US headers
  console.log('\n🇺🇸 Testing US user detection:');
  result = await makeRequest('/subscriptions/plans', 'GET', null, {
    'Accept-Language': 'en-US',
    'CF-IPCountry': 'US',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
  });

  return result;
}

/**
 * Main test runner
 */
async function runTests() {
  console.log('🚀 Starting Recurring Subscription Tests');
  console.log('=' .repeat(80));

  try {
    // Test 1: Currency detection
    await testCurrencyDetection();

    // Test 2: Plan details
    await testPlanDetails();

    // Test 3: Recurring subscriptions for each plan and currency
    for (const currency of TEST_CURRENCIES) {
      for (const planId of TEST_PLANS) {
        await testRecurringSubscription(planId, currency);
        
        // Add delay between tests
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    console.log('\n🎉 All tests completed!');
    console.log('=' .repeat(80));

  } catch (error) {
    console.error('💥 Test suite failed:', error);
  }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  console.log('⚠️  Please update TEST_TOKEN with a valid JWT token before running tests');
  console.log('⚠️  Ensure the server is running on http://localhost:3000');
  console.log('⚠️  Make sure you have a test user account set up');
  console.log('\nTo run tests: node test_recurring_subscription.js\n');
  
  // Uncomment the line below to run tests
  // runTests();
}

export { runTests, testRecurringSubscription, testPlanDetails, testCurrencyDetection };
