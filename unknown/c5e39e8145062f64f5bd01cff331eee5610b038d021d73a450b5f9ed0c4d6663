[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: armeabi-v7a", "file_": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON 'K:\\2025\\thenextdoor\\app\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\113b3829\\armeabi-v7a\\android_gradle_build.json' was up-to-date", "file_": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]