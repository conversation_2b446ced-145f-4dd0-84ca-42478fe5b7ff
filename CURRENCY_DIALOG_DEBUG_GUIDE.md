# Currency Dialog Debug Guide

## 🔧 **Issue: Currency Dialog Not Opening Despite Null Currency in DB**

### **Problem Description**
User reports that the currency dialog is not opening automatically when visiting the subscription page, even though the database shows currency as null.

---

## ✅ **Fixes Applied**

### **1. Enhanced Currency Check Logic**

**File**: `app/src/app/(main)/subscription.js`

**Changes Made**:
- ✅ **Improved null checking**: Now properly handles null/empty currency values from backend
- ✅ **Enhanced logging**: Added comprehensive debug logs to track the flow
- ✅ **Better validation**: Validates both backend and local storage currency data
- ✅ **Debug button**: Added development-only debug button to force currency check

**Key Fix**:
```javascript
// Before: Only checked existence
if (response.data.success && response.data.data.preferences.currency) {

// After: Checks for valid non-null values
if (response.data.success && response.data.data.preferences) {
  backendCurrency = response.data.data.preferences.currency;
  if (backendCurrency && backendCurrency.trim() !== '') {
    hasSavedPreference = true;
  } else {
    console.log('📍 Backend currency is null/empty, treating as no preference');
  }
}
```

---

## 🔍 **Debugging Steps**

### **Step 1: Check Console Logs**

When you visit the subscription page, you should see these logs in order:

```
🚀 Starting fetchSubscriptionData...
🚀 About to check currency...
🔍 Starting currency preference check...
🔍 Checking backend for currency preference...
🔍 Backend response: {success: true, data: {preferences: {currency: null}}}
🔍 Backend currency value: null
📍 Backend currency is null/empty, treating as no preference
🔍 Checking local storage for currency preference...
🔍 Local storage currency: null
📍 No local currency preference found
🎯 No valid currency preference found, showing selection dialog
🎯 Detected currency for dialog: {code: "USD", symbol: "$", name: "US Dollar"}
```

### **Step 2: Check Dialog State**

If logs show the dialog should open but it doesn't, check the dialog state:

```javascript
// In React DevTools, look for:
currencyDialog: {
  visible: true,           // Should be true
  detectedCurrency: {...}, // Should have currency object
  loading: false,          // Should be false
  isManualSelection: false // Should be false for auto-detection
}
```

### **Step 3: Use Debug Button**

In development mode, you'll see a red "DEBUG" button next to the currency selector:

1. **Click the DEBUG button** - This will:
   - Clear local storage currency
   - Directly show currency dialog (no location permission required)
   - Show detailed logs

2. **Check console** for the debug logs:
   ```
   🔧 Force currency check triggered
   🔧 Cleared local storage currency
   🔧 Showing currency dialog directly (skipping location detection)
   🔧 Force currency check completed
   ```

### **Step 4: Console Testing Functions**

In development mode, you can also use these console commands:

```javascript
// Directly show currency dialog
showCurrencyDialog();

// Force currency check (clears storage + shows dialog)
forceCurrencyCheck();
```

---

## 🔧 **Manual Testing Commands**

### **Clear All Currency Data**

Run these in the browser console to reset currency state:

```javascript
// Clear local storage
localStorage.removeItem('user_selected_currency');

// Clear AsyncStorage (React Native)
import AsyncStorage from '@react-native-async-storage/async-storage';
AsyncStorage.removeItem('user_selected_currency');

// Refresh the page
window.location.reload();
```

### **Check Backend Currency**

Make a direct API call to check backend currency:

```javascript
// In browser console or React Native debugger
fetch('/api/auth/preferences', {
  headers: {
    'Authorization': 'Bearer YOUR_TOKEN_HERE',
    'Content-Type': 'application/json'
  }
})
.then(res => res.json())
.then(data => console.log('Backend preferences:', data));
```

### **Force Dialog Open**

If you need to manually trigger the dialog:

```javascript
// In React DevTools, find the component and run:
setCurrencyDialog({
  visible: true,
  detectedCurrency: { code: 'USD', symbol: '$', name: 'US Dollar' },
  loading: false,
  isManualSelection: false
});
```

---

## 🎯 **Expected Behavior**

### **For New Users (No Currency Preference)**:
1. ✅ Visit subscription page
2. ✅ `checkAndConfirmCurrency()` runs automatically
3. ✅ Backend returns null currency
4. ✅ Local storage has no currency
5. ✅ Currency dialog opens automatically
6. ✅ User selects currency
7. ✅ Preference saved to backend and local storage
8. ✅ Plans load with correct pricing

### **For Existing Users (Has Currency Preference)**:
1. ✅ Visit subscription page
2. ✅ `checkAndConfirmCurrency()` runs automatically
3. ✅ Backend returns saved currency OR local storage has currency
4. ✅ No dialog shown
5. ✅ Plans load immediately with saved currency

---

## 🚨 **Common Issues & Solutions**

### **Issue 1: Dialog State Not Updating**
**Symptoms**: Logs show dialog should open but it doesn't appear
**Solution**: Check if `setCurrencyDialog` is being called correctly

### **Issue 2: Backend Returns Unexpected Data**
**Symptoms**: Backend logs show different response than expected
**Solution**: Check User model in database, ensure preferences field exists

### **Issue 3: Local Storage Interference**
**Symptoms**: Dialog doesn't open even after clearing backend
**Solution**: Clear both AsyncStorage and localStorage

### **Issue 4: Component Not Re-rendering**
**Symptoms**: State updates but UI doesn't change
**Solution**: Check React DevTools for state updates, ensure component is mounted

---

## 📋 **Verification Checklist**

- [ ] Database shows currency as null
- [ ] Console shows "No valid currency preference found"
- [ ] Console shows "showing selection dialog"
- [ ] `currencyDialog.visible` is true in React DevTools
- [ ] `CurrencyConfirmationDialog` component is rendered
- [ ] No JavaScript errors in console
- [ ] Component is properly mounted and focused

---

## 🔄 **Next Steps If Still Not Working**

1. **Check Component Mounting**: Ensure the subscription component is properly mounted
2. **Check Modal/Dialog Rendering**: Verify the `CurrencyConfirmationDialog` component is rendering
3. **Check Z-Index Issues**: Ensure dialog isn't hidden behind other elements
4. **Check Platform Differences**: Test on both iOS and Android if using React Native
5. **Check Network Issues**: Ensure API calls are completing successfully

---

## 📞 **Support Information**

If the issue persists after following this guide:

1. **Provide Console Logs**: Copy all console logs from page load
2. **Provide Component State**: Screenshot of React DevTools showing component state
3. **Provide Network Tab**: Screenshot of network requests to `/auth/preferences`
4. **Provide Database State**: Confirm currency field value in database

The enhanced logging and debug tools should help identify exactly where the flow is breaking down.
