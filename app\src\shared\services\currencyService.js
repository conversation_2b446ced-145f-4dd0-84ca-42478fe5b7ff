import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Location from 'expo-location';
import { apiClient } from '../api/apiClient';

/**
 * Currency detection and management service
 * Simplified: India = ₹ (INR), Rest of World = $ (USD)
 */

export const SUPPORTED_CURRENCIES = {
  'IN': { code: 'INR', symbol: '₹', name: 'Indian Rupee', country: 'India' },
  'DEFAULT': { code: 'USD', symbol: '$', name: 'US Dollar', country: 'International' }
};

export class CurrencyService {
  /**
   * Get user's detected or saved currency
   */
  static async getUserCurrency() {
    try {
      // First check backend for user's saved preference
      try {
        const response = await apiClient.get('/auth/preferences');
        if (response.data.success && response.data.data.preferences.currency) {
          const currencyCode = response.data.data.preferences.currency;
          const currency = currencyCode === 'INR' ? SUPPORTED_CURRENCIES.IN : SUPPORTED_CURRENCIES.DEFAULT;
          console.log('📍 Using server-saved currency:', currency);
          return currency;
        }
      } catch (error) {
        console.log('📍 No server preference found, trying local:', error.message);
      }

      // Then check local storage for user's manually selected currency
      const savedCurrency = await AsyncStorage.getItem('user_selected_currency');
      if (savedCurrency) {
        const parsed = JSON.parse(savedCurrency);
        console.log('📍 Using locally saved currency:', parsed);
        return parsed;
      }

      // Try to detect automatically
      const detectedCurrency = await this.detectCurrency();
      console.log('📍 Auto-detected currency:', detectedCurrency);
      return detectedCurrency;

    } catch (error) {
      console.error('Error getting user currency:', error);
      // Fallback to USD
      return SUPPORTED_CURRENCIES.DEFAULT;
    }
  }

  /**
   * Alias for getUserCurrency for backward compatibility
   */
  static async getCurrency() {
    return this.getUserCurrency();
  }

  /**
   * Auto-detect currency based on location
   */
  static async detectCurrency() {
    try {
      // Try to get location permission and detect
      const { status } = await Location.requestForegroundPermissionsAsync();
      
      if (status === 'granted') {
        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.Low,
          timeout: 5000
        });

        // Use reverse geocoding to get country
        const reverseGeocode = await Location.reverseGeocodeAsync({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        });

        if (reverseGeocode && reverseGeocode.length > 0) {
          const country = reverseGeocode[0].isoCountryCode?.toUpperCase();
          console.log('📍 Detected country from location:', country);
          
          if (country === 'IN') {
            return SUPPORTED_CURRENCIES.IN;
          }
        }
      }
    } catch (error) {
      console.log('📍 Location detection failed:', error.message);
    }

    // Fallback: Try to detect from system locale
    try {
      const locale = require('react-native').NativeModules.SettingsManager?.settings?.AppleLocale || 
                   require('react-native').NativeModules.I18nManager?.localeIdentifier ||
                   'en_US';
      
      console.log('📍 System locale:', locale);
      
      if (locale.includes('IN') || locale.includes('_IN')) {
        return SUPPORTED_CURRENCIES.IN;
      }
    } catch (error) {
      console.log('📍 Locale detection failed:', error.message);
    }

    // Default to USD for international users
    return SUPPORTED_CURRENCIES.DEFAULT;
  }

  /**
   * Save user's manually selected currency both locally and on server
   */
  static async saveUserCurrency(currency) {
    try {
      // Save locally first
      await AsyncStorage.setItem('user_selected_currency', JSON.stringify(currency));
      console.log('📍 Saved user currency locally:', currency);

      // Save to server
      try {
        await apiClient.put('/auth/preferences', { currency: currency.code });
        console.log('📍 Saved user currency to server:', currency.code);
      } catch (error) {
        console.log('📍 Failed to save currency to server, but local save succeeded:', error.message);
      }
    } catch (error) {
      console.error('Error saving user currency:', error);
    }
  }

  /**
   * Show currency selection dialog (for when auto-detection fails or user wants to change)
   */
  static async showCurrencySelector() {
    return new Promise((resolve) => {
      const { Alert } = require('react-native');
      
      Alert.alert(
        'Select Your Currency',
        'Choose your preferred currency for pricing:',
        [
          {
            text: '🇮🇳 Indian Rupee (₹)',
            onPress: async () => {
              const currency = SUPPORTED_CURRENCIES.IN;
              await this.saveUserCurrency(currency);
              resolve(currency);
            }
          },
          {
            text: '🌍 US Dollar ($)',
            onPress: async () => {
              const currency = SUPPORTED_CURRENCIES.DEFAULT;
              await this.saveUserCurrency(currency);
              resolve(currency);
            }
          }
        ],
        { cancelable: false }
      );
    });
  }

  /**
   * Format price with currency symbol
   */
  static formatPrice(price, currencyInfo, interval = 'month', intervalCount = 1) {
    const { symbol } = currencyInfo;
    
    if (intervalCount === 3) return `${symbol}${price}/quarter`;
    if (intervalCount === 12) return `${symbol}${price}/year`;
    return `${symbol}${price}/month`;
  }

  /**
   * Set user's currency preference
   * Saves both locally and to backend
   */
  static async setCurrency(currency) {
    try {
      // Save locally
      await AsyncStorage.setItem('user_selected_currency', JSON.stringify(currency));
      console.log('📍 Saved currency locally:', currency);

      // Save to backend if user is authenticated
      try {
        const response = await apiClient.put('/auth/preferences', {
          currency: currency.code
        });
        console.log('📍 Saved currency to backend successfully:', currency.code, response.data);
      } catch (error) {
        console.error('📍 Failed to save currency to backend:', error.response?.data || error.message);
        // Don't throw error - local storage is sufficient for now
      }

      return currency;
    } catch (error) {
      console.error('Error setting currency:', error);
      throw error;
    }
  }

  /**
   * Reset currency selection (useful for testing or changing preferences)
   */
  static async resetCurrencySelection() {
    try {
      await AsyncStorage.removeItem('user_selected_currency');
      await AsyncStorage.removeItem('has_seen_currency_dialog');
      console.log('📍 Reset currency selection');
    } catch (error) {
      console.error('Error resetting currency selection:', error);
    }
  }

  /**
   * Convert INR price to other currency (simple conversion)
   */
  static convertPrice(inrPrice, targetCurrency) {
    if (targetCurrency.code === 'INR') {
      return inrPrice;
    }
    
    // Simple conversion: 1 USD = ~83 INR (approximate)
    if (targetCurrency.code === 'USD') {
      return Math.round((inrPrice / 95) * 100) / 100; // Round to 2 decimal places
    }
    
    return inrPrice; // Fallback
  }
}

// Export the service
// export { CurrencyService };
// export default CurrencyService;
