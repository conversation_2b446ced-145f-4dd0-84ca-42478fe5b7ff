/**
 * Test script for currency detection edge cases
 * Tests various error scenarios and fallback mechanisms
 */

import { CurrencyService, SUPPORTED_CURRENCIES } from './app/src/shared/services/currencyService';

// Mock implementations for testing edge cases
const mockLocation = {
  requestForegroundPermissionsAsync: jest.fn(),
  getCurrentPositionAsync: jest.fn(),
  reverseGeocodeAsync: jest.fn(),
};

const mockAsyncStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
};

const mockApiClient = {
  get: jest.fn(),
  put: jest.fn(),
};

// Test scenarios
const TEST_SCENARIOS = {
  LOCATION_PERMISSION_DENIED: 'location_permission_denied',
  LOCATION_TIMEOUT: 'location_timeout',
  LOCATION_UNAVAILABLE: 'location_unavailable',
  REVERSE_GEOCODING_FAILED: 'reverse_geocoding_failed',
  NETWORK_ERROR: 'network_error',
  BACKEND_ERROR: 'backend_error',
  CORRUPTED_LOCAL_STORAGE: 'corrupted_local_storage',
  INVALID_LOCALE: 'invalid_locale',
  ALL_METHODS_FAIL: 'all_methods_fail',
};

/**
 * Test currency detection with location permission denied
 */
async function testLocationPermissionDenied() {
  console.log('\n🧪 Testing: Location Permission Denied');
  console.log('='.repeat(50));

  // Mock permission denied
  mockLocation.requestForegroundPermissionsAsync.mockResolvedValue({ status: 'denied' });

  try {
    const result = await CurrencyService.detectCurrency();
    console.log('✅ Result:', result);
    console.log('📋 Expected: Should fallback to locale/timezone detection');
    
    return result.code === 'USD' || result.code === 'INR'; // Should return valid currency
  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  }
}

/**
 * Test currency detection with location timeout
 */
async function testLocationTimeout() {
  console.log('\n🧪 Testing: Location Timeout');
  console.log('='.repeat(50));

  // Mock permission granted but location timeout
  mockLocation.requestForegroundPermissionsAsync.mockResolvedValue({ status: 'granted' });
  mockLocation.getCurrentPositionAsync.mockRejectedValue({
    code: 'E_LOCATION_TIMEOUT',
    message: 'Location request timed out'
  });

  try {
    const result = await CurrencyService.detectCurrency();
    console.log('✅ Result:', result);
    console.log('📋 Expected: Should fallback to locale detection');
    
    return result.code === 'USD' || result.code === 'INR';
  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  }
}

/**
 * Test currency detection with location unavailable
 */
async function testLocationUnavailable() {
  console.log('\n🧪 Testing: Location Services Unavailable');
  console.log('='.repeat(50));

  mockLocation.requestForegroundPermissionsAsync.mockResolvedValue({ status: 'granted' });
  mockLocation.getCurrentPositionAsync.mockRejectedValue({
    code: 'E_LOCATION_UNAVAILABLE',
    message: 'Location services are disabled'
  });

  try {
    const result = await CurrencyService.detectCurrency();
    console.log('✅ Result:', result);
    
    return result.code === 'USD' || result.code === 'INR';
  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  }
}

/**
 * Test currency detection with reverse geocoding failure
 */
async function testReverseGeocodingFailed() {
  console.log('\n🧪 Testing: Reverse Geocoding Failed');
  console.log('='.repeat(50));

  mockLocation.requestForegroundPermissionsAsync.mockResolvedValue({ status: 'granted' });
  mockLocation.getCurrentPositionAsync.mockResolvedValue({
    coords: { latitude: 28.6139, longitude: 77.2090 } // Delhi coordinates
  });
  mockLocation.reverseGeocodeAsync.mockResolvedValue([]); // Empty result

  try {
    const result = await CurrencyService.detectCurrency();
    console.log('✅ Result:', result);
    
    return result.code === 'USD' || result.code === 'INR';
  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  }
}

/**
 * Test getUserCurrency with network error
 */
async function testNetworkError() {
  console.log('\n🧪 Testing: Network Error');
  console.log('='.repeat(50));

  // Mock network error for backend call
  mockApiClient.get.mockRejectedValue({
    code: 'NETWORK_ERROR',
    message: 'Network request failed'
  });

  // Mock no local storage
  mockAsyncStorage.getItem.mockResolvedValue(null);

  try {
    const result = await CurrencyService.getUserCurrency();
    console.log('✅ Result:', result);
    console.log('📋 Expected: Should fallback to auto-detection');
    
    return result.code === 'USD' || result.code === 'INR';
  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  }
}

/**
 * Test getUserCurrency with backend error
 */
async function testBackendError() {
  console.log('\n🧪 Testing: Backend Error (500)');
  console.log('='.repeat(50));

  mockApiClient.get.mockRejectedValue({
    response: { status: 500 },
    message: 'Internal server error'
  });

  mockAsyncStorage.getItem.mockResolvedValue(null);

  try {
    const result = await CurrencyService.getUserCurrency();
    console.log('✅ Result:', result);
    
    return result.code === 'USD' || result.code === 'INR';
  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  }
}

/**
 * Test getUserCurrency with corrupted local storage
 */
async function testCorruptedLocalStorage() {
  console.log('\n🧪 Testing: Corrupted Local Storage');
  console.log('='.repeat(50));

  mockApiClient.get.mockRejectedValue({ response: { status: 404 } });
  mockAsyncStorage.getItem.mockResolvedValue('invalid-json-data');

  try {
    const result = await CurrencyService.getUserCurrency();
    console.log('✅ Result:', result);
    console.log('📋 Expected: Should clear corrupted data and fallback');
    
    // Should have attempted to clear corrupted data
    expect(mockAsyncStorage.removeItem).toHaveBeenCalledWith('user_selected_currency');
    
    return result.code === 'USD' || result.code === 'INR';
  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  }
}

/**
 * Test setCurrency with various error scenarios
 */
async function testSetCurrencyErrors() {
  console.log('\n🧪 Testing: setCurrency Error Scenarios');
  console.log('='.repeat(50));

  const testCurrency = SUPPORTED_CURRENCIES.IN;

  // Test 1: Local storage failure
  console.log('📋 Test 1: Local storage failure');
  mockAsyncStorage.setItem.mockRejectedValue(new Error('Storage quota exceeded'));
  
  try {
    await CurrencyService.setCurrency(testCurrency);
    console.log('❌ Should have thrown error for local storage failure');
    return false;
  } catch (error) {
    console.log('✅ Correctly threw error for local storage failure');
  }

  // Test 2: Backend failure but local success
  console.log('📋 Test 2: Backend failure but local success');
  mockAsyncStorage.setItem.mockResolvedValue();
  mockApiClient.put.mockRejectedValue({
    response: { status: 500 },
    message: 'Server error'
  });

  try {
    const result = await CurrencyService.setCurrency(testCurrency);
    console.log('✅ Result:', result);
    console.log('📋 Expected: Success with warnings');
    
    return result.success && result.warnings.length > 0;
  } catch (error) {
    console.error('❌ Should not have thrown error for backend failure');
    return false;
  }
}

/**
 * Run all edge case tests
 */
async function runAllTests() {
  console.log('🚀 Starting Currency Detection Edge Case Tests');
  console.log('='.repeat(80));

  const tests = [
    { name: 'Location Permission Denied', fn: testLocationPermissionDenied },
    { name: 'Location Timeout', fn: testLocationTimeout },
    { name: 'Location Unavailable', fn: testLocationUnavailable },
    { name: 'Reverse Geocoding Failed', fn: testReverseGeocodingFailed },
    { name: 'Network Error', fn: testNetworkError },
    { name: 'Backend Error', fn: testBackendError },
    { name: 'Corrupted Local Storage', fn: testCorruptedLocalStorage },
    { name: 'setCurrency Errors', fn: testSetCurrencyErrors },
  ];

  const results = [];

  for (const test of tests) {
    try {
      const passed = await test.fn();
      results.push({ name: test.name, passed });
      console.log(`${passed ? '✅' : '❌'} ${test.name}: ${passed ? 'PASSED' : 'FAILED'}`);
    } catch (error) {
      results.push({ name: test.name, passed: false, error: error.message });
      console.log(`❌ ${test.name}: FAILED (${error.message})`);
    }

    // Add delay between tests
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  // Summary
  console.log('\n📊 Test Results Summary');
  console.log('='.repeat(80));
  
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  
  console.log(`✅ Passed: ${passed}/${total}`);
  console.log(`❌ Failed: ${total - passed}/${total}`);
  
  if (passed === total) {
    console.log('🎉 All edge case tests passed!');
  } else {
    console.log('⚠️ Some tests failed. Review the implementation.');
  }

  return results;
}

// Export for use in test suites
export {
  runAllTests,
  testLocationPermissionDenied,
  testLocationTimeout,
  testLocationUnavailable,
  testReverseGeocodingFailed,
  testNetworkError,
  testBackendError,
  testCorruptedLocalStorage,
  testSetCurrencyErrors,
  TEST_SCENARIOS
};
