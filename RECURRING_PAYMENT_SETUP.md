# Recurring Payment System Setup Guide

## Overview
This implementation provides a complete recurring payment system with:
- ✅ **Automatic Payment Reminders** (3 days and 1 day before renewal)
- ✅ **Failed Payment Handling** with retry logic
- ✅ **Email Notifications** for all payment events
- ✅ **True Recurring Subscriptions** via Razorpay
- ✅ **Subscription Lifecycle Management** (active → expired → cancelled)

## Environment Variables Required

Add these to your `.env` file:

### Email Configuration
```env
# Enable email notifications
ENABLE_EMAIL_NOTIFICATIONS=true

# Email service configuration
EMAIL_SERVICE=gmail
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password
EMAIL_FROM="UNextDoor <<EMAIL>>"

# Frontend URL for email links
FRONTEND_URL=http://localhost:3000
CLIENT_URL=http://localhost:3000
```

### Payment Configuration (Already Required)
```env
# Razorpay credentials
RAZORPAY_KEY_ID=rzp_test_xxxxx
RAZORPAY_KEY_SECRET=your_secret_key
RAZORPAY_WEBHOOK_SECRET=your_webhook_secret

# Enable payments
ENABLE_PAYMENTS=true
```

## API Endpoints

### 1. Create Recurring Subscription
```http
POST /api/subscriptions/create-recurring
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "planId": "basic_monthly",
  "customerInfo": {
    "name": "John Doe",
    "phone": "+1234567890"
  }
}
```

### 2. Update Auto-Renewal Setting
```http
PUT /api/subscriptions/auto-renewal
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "autoRenewal": false
}
```

## Email Templates Included

### 1. Payment Reminders
- **3 days before renewal**: Friendly reminder with subscription details
- **1 day before renewal**: Urgent reminder with payment method check

### 2. Payment Status Notifications
- **Payment Success**: Confirmation with new billing date
- **Payment Failed**: Instructions to update payment method
- **Manual Renewal Required**: Payment link when auto-payment fails

### 3. Subscription Status Changes
- **Subscription Suspended**: After multiple payment failures
- **Subscription Expired**: When subscription ends
- **Subscription Cancelled**: User or system cancellation

## Scheduled Jobs

The system runs the following background jobs:

### Daily (9:00 AM)
- Send payment reminders for upcoming renewals
- Process expired subscriptions

### Every 2 Hours
- Check for renewals due today
- Process subscription renewals

### Every 6 Hours
- Retry failed payments (up to 3 attempts)
- Recovery payment processing

## How Recurring Payments Work

### Current Flow (One-time Payments):
1. User selects plan → Creates payment order
2. Payment succeeds → Subscription activated for period
3. Period expires → User must manually renew

### New Recurring Flow:
1. User selects plan → Creates Razorpay subscription
2. First payment succeeds → Subscription activated
3. **Auto-renewal process**:
   - 3 days before: Email reminder sent
   - 1 day before: Final reminder sent
   - Renewal date: Automatic payment attempt
   - Payment success: Subscription extended automatically
   - Payment failure: Retry logic + user notification

### Payment Failure Handling:
1. **First failure**: Email notification + retry in 24 hours
2. **Second failure**: Another email + retry in 24 hours  
3. **Third failure**: Subscription suspended + grace period
4. **After grace period**: Subscription expired

## User Experience

### What Users Get:
- ✅ **Automatic renewals** - No manual action needed
- ✅ **Advance notifications** - Know exactly when payments are coming
- ✅ **Payment failure alerts** - Immediate notification if payment fails
- ✅ **Grace periods** - Time to fix payment issues before service interruption
- ✅ **Easy management** - Can disable auto-renewal anytime

### Email Examples:

**Payment Reminder (3 days before):**
> 🔔 Payment Reminder
> 
> Hi John! Your Basic Monthly subscription will be renewed automatically in 3 days.
> 
> Plan: Basic Monthly
> Amount: $9.99
> Renewal Date: January 15, 2025
> 
> No action required - we'll charge your saved payment method automatically.

**Payment Failed:**
> ⚠️ Payment Failed
> 
> We were unable to process your payment for Basic Monthly subscription.
> 
> Your subscription is still active, but we'll try again in 3 days. 
> 
> [Update Payment Method] button

## Testing

### Test the System:
1. **Create a test subscription** with short renewal period
2. **Monitor email notifications** in development
3. **Test payment failures** by using invalid card details
4. **Check renewal processing** by reviewing server logs

### Development Setup:
```bash
# Install dependencies (already done)
cd server
npm install

# Set up environment variables
cp .env.example .env
# Edit .env with your email and payment credentials

# Start the server
npm run dev
```

### Webhook Testing:
Use ngrok to test Razorpay webhooks locally:
```bash
# Install ngrok
npm install -g ngrok

# Expose your local server
ngrok http 3001

# Use the ngrok URL in Razorpay webhook settings
# https://abc123.ngrok.io/api/webhooks/payment
```

## Production Deployment

### Email Service Setup:
1. **Gmail**: Use App Passwords for authentication
2. **SendGrid**: More reliable for production
3. **AWS SES**: Cost-effective for high volume

### Monitoring:
- **Email delivery**: Track bounce rates and failures
- **Payment success rates**: Monitor renewal success/failure rates
- **Subscription health**: Track cancellation reasons

### Security:
- ✅ **Webhook verification**: All webhooks are cryptographically verified
- ✅ **JWT authentication**: All API endpoints are protected  
- ✅ **Input validation**: All user inputs are validated
- ✅ **Error handling**: Graceful error handling with logging

## Support

The system includes comprehensive logging for debugging:
- Email sending status
- Payment processing results
- Subscription renewal attempts
- Error tracking with context

All critical operations are logged with appropriate log levels for monitoring in production.
