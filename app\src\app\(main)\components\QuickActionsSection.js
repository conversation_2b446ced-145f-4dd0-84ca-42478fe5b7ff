import React from 'react';
import { View, Animated } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../../shared/context/ThemeContext';
import { 
  Text, 
  Heading, 
  ModernCard, 
  Row 
} from '../../../shared/components';
import { useRouter } from 'expo-router';
import { AI_TUTOR_NAME } from '../../../constants/appConstants';

const QuickActionsSection = ({ fadeAnim, scaleAnim, currentLesson }) => {
  const { theme } = useTheme();
  const router = useRouter();

  return (
    <Animated.View
      style={{
        opacity: fadeAnim,
        transform: [{ scale: scaleAnim }],
        paddingHorizontal: theme.spacing.md,
      }}
    >
      <Heading
        level="h3"
        style={{
          marginBottom: theme.spacing.sm,
          color: theme.colors.brandNavy,
          fontFamily: theme.typography.fontFamily.semibold,
        }}
      >
        Quick Actions
      </Heading>
      <Row justify="center">
        <ModernCard
          variant="flat"
          interactive
          onPress={() =>
            router.push({
              pathname: "/tutor/standalone-conversation",
              params: {
                scenarioId: "general-conversation",
                title: `Talk with ${AI_TUTOR_NAME}`,
                level: "Beginner",
              },
            })
          }
          style={{
            flex: 1,
            marginRight: theme.spacing.xs,
            minHeight: 120,
            justifyContent: "center",
            backgroundColor: theme.colors.brandGreen + "08",
            borderWidth: 1,
            borderColor: theme.colors.brandGreen + "20",
          }}
        >
          <View
            style={{
              width: 48,
              height: 48,
              borderRadius: 24,
              backgroundColor: theme.colors.brandGreen + "20",
              alignItems: "center",
              justifyContent: "center",
              marginBottom: theme.spacing.sm,
              alignSelf: "center",
            }}
          >
            <Ionicons
              name="mic-outline"
              size={24}
              color={theme.colors.brandGreen}
            />
          </View>
          <Text
            weight="semibold"
            numberOfLines={1}
            ellipsizeMode="tail"
            align="center"
            style={{
              color: theme.colors.brandNavy,
              fontFamily: theme.typography.fontFamily.semibold,
            }}
          >
            {`Talk with ${AI_TUTOR_NAME}`}
          </Text>
          <Text
            variant="caption"
            weight="medium"
            numberOfLines={1}
            align="center"
            style={{
              color: theme.colors.neutral[600],
              fontFamily: theme.typography.fontFamily.medium,
            }}
          >
            Practice conversation
          </Text>
        </ModernCard>
        <ModernCard
          variant="flat"
          interactive
          onPress={() => {
            if (currentLesson) {
              router.push(`/tutor/lesson/${currentLesson.lessonId}`);
            } else {
              router.push("/tutor/lessons");
            }
          }}
          style={{
            flex: 1,
            marginLeft: theme.spacing.xs,
            minHeight: 120,
            justifyContent: "center",
            backgroundColor: theme.colors.brandNavy + "08",
            borderWidth: 1,
            borderColor: theme.colors.brandNavy + "20",
          }}
        >
          <View
            style={{
              width: 48,
              height: 48,
              borderRadius: 24,
              backgroundColor: theme.colors.brandNavy + "20",
              alignItems: "center",
              justifyContent: "center",
              marginBottom: theme.spacing.sm,
              alignSelf: "center",
            }}
          >
            <Ionicons
              name="book-outline"
              size={24}
              color={theme.colors.brandNavy}
            />
          </View>
          <Text
            weight="semibold"
            numberOfLines={1}
            ellipsizeMode="tail"
            align="center"
            style={{
              color: theme.colors.brandNavy,
              fontFamily: theme.typography.fontFamily.semibold,
            }}
          >
            Current Lesson
          </Text>
          <Text
            variant="caption"
            weight="medium"
            numberOfLines={2}
            align="center"
            style={{
              color: theme.colors.neutral[600],
              fontFamily: theme.typography.fontFamily.medium,
            }}
          >
            {currentLesson ? currentLesson.name : "Start learning"}
          </Text>
        </ModernCard>
      </Row>
    </Animated.View>
  );
};

export default QuickActionsSection;
