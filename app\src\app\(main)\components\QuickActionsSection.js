import React from 'react';
import { View, Animated, TouchableOpacity, LinearGradient } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../../shared/context/ThemeContext';
import {
  Text,
  Heading,
  Row,
  Column
} from '../../../shared/components';
import { useRouter } from 'expo-router';

const QuickActionsSection = ({ fadeAnim, scaleAnim, currentLesson }) => {
  const { theme } = useTheme();
  const router = useRouter();

  return (
    <Animated.View
      style={{
        opacity: fadeAnim,
        transform: [{ scale: scaleAnim }],
        paddingHorizontal: theme.spacing.md,
      }}
    >
      <Row
        justify="space-between"
        align="center"
        style={{ marginBottom: theme.spacing.lg }}
      >
        <Column>
          <Text
            variant="caption"
            weight="medium"
            style={{
              color: theme.colors.neutral[500],
              fontFamily: theme.typography.fontFamily.medium,
              textTransform: "uppercase",
              letterSpacing: 0.5,
              fontSize: 11,
              marginBottom: -18,
            }}
          >
            Quick Actions
          </Text>
          <Heading
            level="h3"
            style={{
              color: theme.colors.brandNavy,
              fontFamily: theme.typography.fontFamily.bold,
              fontSize: 20,
            }}
          >
            Start Learning
          </Heading>
        </Column>
      </Row>

      {/* Enhanced Action Cards */}
      <View style={{ gap: theme.spacing.md }}>
        {/* Talk with Cooper - Primary Action */}
        <TouchableOpacity
          onPress={() =>
            router.push({
              pathname: "/tutor/standalone-conversation",
              params: {
                scenarioId: "general-conversation",
                title: "Talk with Cooper",
                level: "Beginner",
              },
            })
          }
          style={{
            backgroundColor: theme.colors.brandGreen,
            borderRadius: 20,
            padding: theme.spacing.lg,
            shadowColor: theme.colors.brandGreen,
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.3,
            shadowRadius: 12,
            elevation: 8,
          }}
        >
          <Row align="center" justify="space-between">
            <Row align="center" style={{ flex: 1 }}>
              <View
                style={{
                  width: 56,
                  height: 56,
                  borderRadius: 28,
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  alignItems: "center",
                  justifyContent: "center",
                  marginRight: theme.spacing.md,
                }}
              >
                <Ionicons
                  name="mic"
                  size={28}
                  color="white"
                />
              </View>
              <Column style={{ flex: 1 }}>
                <Text
                  weight="bold"
                  style={{
                    color: "white",
                    fontFamily: theme.typography.fontFamily.bold,
                    fontSize: 18,
                    marginBottom: 4,
                  }}
                >
                  Talk with Cooper
                </Text>
                <Text
                  style={{
                    color: "rgba(255, 255, 255, 0.9)",
                    fontFamily: theme.typography.fontFamily.medium,
                    fontSize: 14,
                  }}
                >
                  Practice conversation with AI tutor
                </Text>
              </Column>
            </Row>
            <View
              style={{
                width: 40,
                height: 40,
                borderRadius: 20,
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <Ionicons
                name="arrow-forward"
                size={20}
                color="white"
              />
            </View>
          </Row>
        </TouchableOpacity>

        {/* Current Lesson - Secondary Action */}
        <TouchableOpacity
          onPress={() => {
            if (currentLesson) {
              router.push(`/tutor/lesson/${currentLesson.lessonId}`);
            } else {
              router.push("/tutor/lessons");
            }
          }}
          style={{
            backgroundColor: theme.colors.brandWhite,
            borderRadius: 20,
            padding: theme.spacing.lg,
            borderWidth: 2,
            borderColor: theme.colors.brandNavy + "15",
            shadowColor: theme.colors.brandNavy,
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 8,
            elevation: 4,
          }}
        >
          <Row align="center" justify="space-between">
            <Row align="center" style={{ flex: 1 }}>
              <View
                style={{
                  width: 56,
                  height: 56,
                  borderRadius: 28,
                  backgroundColor: theme.colors.brandNavy + "15",
                  alignItems: "center",
                  justifyContent: "center",
                  marginRight: theme.spacing.md,
                }}
              >
                <Ionicons
                  name="book"
                  size={28}
                  color={theme.colors.brandNavy}
                />
              </View>
              <Column style={{ flex: 1 }}>
                <Text
                  weight="bold"
                  style={{
                    color: theme.colors.brandNavy,
                    fontFamily: theme.typography.fontFamily.bold,
                    fontSize: 18,
                    marginBottom: 4,
                  }}
                >
                  {currentLesson ? "Continue Lesson" : "Start Learning"}
                </Text>
                <Text
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  style={{
                    color: theme.colors.neutral[600],
                    fontFamily: theme.typography.fontFamily.medium,
                    fontSize: 14,
                  }}
                >
                  {currentLesson ? currentLesson.name : "Begin your Korean journey"}
                </Text>
              </Column>
            </Row>
            <View
              style={{
                width: 40,
                height: 40,
                borderRadius: 20,
                backgroundColor: theme.colors.brandNavy + "10",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <Ionicons
                name="arrow-forward"
                size={20}
                color={theme.colors.brandNavy}
              />
            </View>
          </Row>
        </TouchableOpacity>

        {/* Additional Quick Actions Row */}
        <Row justify="space-between" style={{ gap: theme.spacing.sm }}>
          {/* Vocabulary Quick Access */}
          <TouchableOpacity
            onPress={() => router.push("/vocabulary")}
            style={{
              flex: 1,
              backgroundColor: theme.colors.brandWhite,
              borderRadius: 16,
              padding: theme.spacing.md,
              borderWidth: 1,
              borderColor: "#2196F3" + "20",
              alignItems: "center",
            }}
          >
            <View
              style={{
                width: 44,
                height: 44,
                borderRadius: 22,
                backgroundColor: "#2196F3" + "15",
                alignItems: "center",
                justifyContent: "center",
                marginBottom: theme.spacing.sm,
              }}
            >
              <Ionicons
                name="library"
                size={22}
                color="#2196F3"
              />
            </View>
            <Text
              weight="semibold"
              align="center"
              style={{
                color: theme.colors.brandNavy,
                fontFamily: theme.typography.fontFamily.semibold,
                fontSize: 14,
                marginBottom: 2,
              }}
            >
              Vocabulary
            </Text>
            <Text
              variant="caption"
              align="center"
              style={{
                color: theme.colors.neutral[500],
                fontFamily: theme.typography.fontFamily.regular,
                fontSize: 12,
              }}
            >
              Learn words
            </Text>
          </TouchableOpacity>

          {/* Games Quick Access */}
          <TouchableOpacity
            onPress={() => router.push("/games")}
            style={{
              flex: 1,
              backgroundColor: theme.colors.brandWhite,
              borderRadius: 16,
              padding: theme.spacing.md,
              borderWidth: 1,
              borderColor: "#FF9800" + "20",
              alignItems: "center",
            }}
          >
            <View
              style={{
                width: 44,
                height: 44,
                borderRadius: 22,
                backgroundColor: "#FF9800" + "15",
                alignItems: "center",
                justifyContent: "center",
                marginBottom: theme.spacing.sm,
              }}
            >
              <Ionicons
                name="game-controller"
                size={22}
                color="#FF9800"
              />
            </View>
            <Text
              weight="semibold"
              align="center"
              style={{
                color: theme.colors.brandNavy,
                fontFamily: theme.typography.fontFamily.semibold,
                fontSize: 14,
                marginBottom: 2,
              }}
            >
              Games
            </Text>
            <Text
              variant="caption"
              align="center"
              style={{
                color: theme.colors.neutral[500],
                fontFamily: theme.typography.fontFamily.regular,
                fontSize: 12,
              }}
            >
              Play & learn
            </Text>
          </TouchableOpacity>

          {/* Progress Quick Access */}
          <TouchableOpacity
            onPress={() => router.push("/tutor/progress")}
            style={{
              flex: 1,
              backgroundColor: theme.colors.brandWhite,
              borderRadius: 16,
              padding: theme.spacing.md,
              borderWidth: 1,
              borderColor: "#9C27B0" + "20",
              alignItems: "center",
            }}
          >
            <View
              style={{
                width: 44,
                height: 44,
                borderRadius: 22,
                backgroundColor: "#9C27B0" + "15",
                alignItems: "center",
                justifyContent: "center",
                marginBottom: theme.spacing.sm,
              }}
            >
              <Ionicons
                name="analytics"
                size={22}
                color="#9C27B0"
              />
            </View>
            <Text
              weight="semibold"
              align="center"
              style={{
                color: theme.colors.brandNavy,
                fontFamily: theme.typography.fontFamily.semibold,
                fontSize: 14,
                marginBottom: 2,
              }}
            >
              Progress
            </Text>
            <Text
              variant="caption"
              align="center"
              style={{
                color: theme.colors.neutral[500],
                fontFamily: theme.typography.fontFamily.regular,
                fontSize: 12,
              }}
            >
              View stats
            </Text>
          </TouchableOpacity>
        </Row>
      </View>
    </Animated.View>
  );
};

export default QuickActionsSection;
