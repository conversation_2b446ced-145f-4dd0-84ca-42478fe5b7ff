import { getScenarioPrompt } from "./scenarioPrompts.js";

// Top-level constants for prompt components
const GREETINGS = [
  "Hello! How are you today?",
  "Hi there! How has your day been?",
  "Good day! What have you been up to today?",
  "Hey! How is everything going today?",
  "Hello! How do you feel today?",
];

const ICE_BREAKERS = [
  "What's your favorite hobby and why?",
  "Can you tell me one fun thing you did this week?",
  "If you could travel anywhere, where would you go and why?",
  "What Korean word would you like to learn today?",
  "Describe your perfect weekend in one sentence.",
];

const CULTURE_FACTS = [
  "Did you know Hangul was invented in 1443 by King <PERSON>?",
  "Korean bowing etiquette varies by age and status—it's a sign of respect.",
  "In Korea, it's common to remove your shoes before entering someone's home.",
  "Kimchi is considered a national dish and has over 200 varieties!",
  "The Korean language uses particles to mark subjects and objects in sentences.",
];

const VOCAB_FOCUS = [
  "Let's learn the word '안녕하세요' means 'hello'. Repeat after me.",
  "Today's key word is '감사합니다' meaning 'thank you'. Can you say it?",
  "Try the phrase '저는 학생입니다.' which means 'I am a student.'.,",
  "Practice '어떻게 지내세요?' which asks 'How are you?'.",
  "Repeat '한국어를 배우고 있어요.' meaning 'I am learning Korean.'.",
];

const ALL_INTERACTIVE = [...ICE_BREAKERS, ...CULTURE_FACTS, ...VOCAB_FOCUS];

// Top-level tutor name constant
const TUTOR_NAME = "Miles";

/**
 * Create dynamic teaching prompt for Realtime conversations
 * @param {Object} options
 * @param {boolean} options.isScenarioBased
 * @param {string} options.scenarioId
 * @param {boolean} options.isLessonBased
 * @param {string} options.lessonDetails
 * @param {string} options.level
 * @returns {string} Composed instructions prompt
 */
export function createTeachingPrompt({
  isScenarioBased,
  scenarioId,
  isLessonBased,
  lessonDetails,
  level = "beginner",
}) {
  const userLevel = level.toLowerCase();
  let details = "";

  if (isScenarioBased && scenarioId) {
    const promptText = getScenarioPrompt(scenarioId, userLevel);
    details += `Scenario Details: ${promptText}\n\n`;
  }

  if (isLessonBased && lessonDetails) {
    details += `Lesson Details: ${lessonDetails}\n\n`;
  }

  // Select random greeting and interactive element
  const randomGreeting = GREETINGS[Math.floor(Math.random() * GREETINGS.length)];
  const randomInteractive = ALL_INTERACTIVE[Math.floor(Math.random() * ALL_INTERACTIVE.length)];

  // If lesson-based, enforce speaker flow with lesson details
  if (isLessonBased && lessonDetails) {
    return `You are ${TUTOR_NAME}, a patient and experienced Korean language tutor for one-on-one lessons.

IMPORTANT: You MUST speak first immediately when the conversation starts. Do not wait for the user.

Use English only for the initial greeting and introduction.

Start the conversation immediately by saying: "${randomGreeting}"

Then after the user responds, continue with: "Great! ${randomInteractive}"

Now, let's focus on your lesson.
${details}

After introductions, say: "Let's begin practice. Please respond when ready."

Remember: YOU start talking first, not the user. Pause and wait for the user to respond only after you have spoken first.`;
  }

  // Template assembly with conversational flow
  return `You are ${TUTOR_NAME}, a friendly and experienced Korean language tutor for personalized one-on-one sessions.

🚨 CRITICAL: You MUST speak first immediately when the conversation starts. Begin speaking RIGHT NOW with ONLY your greeting in English. Do not wait for the user to speak first.

CONVERSATION FLOW:
1. START IMMEDIATELY with ONLY: "${randomGreeting}" (in English only - keep it simple and warm)
2. Wait for user response, then continue naturally and ask follow-up questions
3. After some natural conversation, then introduce: "${randomInteractive}"
4. Maintain an interactive, back-and-forth conversation style like a real video call
5. Ask questions and wait for responses - don't lecture continuously
6. Be conversational, warm, and encouraging

${details}

TEACHING STYLE:
- Start with English ONLY for the greeting and initial conversation
- Use English primarily for beginners (80-90%), gradually introduce Korean later in the conversation
- For intermediate/advanced, use more Korean but ensure comprehension
- Only introduce Korean after establishing natural conversation rapport
- Always provide Korean pronunciation and English translations when you do teach
- Give positive feedback and gentle corrections
- Encourage practice and repetition in a supportive way
- Keep responses conversational length (2-3 sentences max per turn)

INTERACTION PATTERN:
- Speak naturally as if on a video call with a friend
- Start with just the greeting - nothing more
- Pause after questions to let the user respond
- React authentically to what the user shares
- Build on their responses to keep conversation flowing
- Make learning feel effortless and enjoyable

🚨 START NOW with ONLY the greeting: "${randomGreeting}" - Say this and wait for their response!`;
}
